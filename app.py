from PIL import Image, ImageDraw, ImageFont, ImageEnhance
import matplotlib.pyplot as plt

# <PERSON><PERSON><PERSON><PERSON> yolunu kendi dos<PERSON> de<PERSON>ştir
image_path = "test.jpg"
original = Image.open(image_path).convert("RGBA")

# Renk ve kontrast iyileştirmesi
enhancer = ImageEnhance.Color(original)
colored = enhancer.enhance(1.2)
enhancer = ImageEnhance.Contrast(colored)
contrasted = enhancer.enhance(1.15)

# <PERSON><PERSON><PERSON> katman ve yazı alanı
txt_layer = Image.new("RGBA", contrasted.size, (255,255,255,0))
draw = ImageDraw.Draw(txt_layer)

# Fontlar (sistemine göre ayarlayabilirsin)
font_title = ImageFont.truetype("DejaVuSans-Bold.ttf", 50)
font_sub = ImageFont.truetype("DejaVuSans.ttf", 28)

# Yazılar
title = "254.000 KM’de Hâlâ Güçlü"
subtitle = "Fiat Stilo 1.6 16V | Bordeaux Old Rose\n2010'dan beri aynı elde"

# Konumlama
W, H = contrasted.size
draw.text((W/2, H*0.08), title, font=font_title, anchor="mm", fill=(255,255,255,240))
draw.text((W/2, H*0.18), subtitle, font=font_sub, anchor="mm", fill=(255,255,255,200))

# Katmanları birleştir
combined = Image.alpha_composite(contrasted, txt_layer)
final_image = combined.convert("RGB")
final_image.save("fiat_stilo_social.jpg")

# Önizleme
plt.imshow(final_image)
plt.axis("off")
plt.title("Sosyal Medya Görseli Önizleme")
plt.show()
