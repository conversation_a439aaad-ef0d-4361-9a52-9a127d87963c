from PIL import Image, ImageDraw, ImageFont, ImageEnhance
import matplotlib.pyplot as plt

# Fotoğrafı yükle (aracin_fotografi.jpg dosyasını kendi fotoğrafınla değiştir)
image_path = "test.jpg"
original = Image.open(image_path).convert("RGBA")

# Görse<PERSON> daha canlı ve kontrastlı yap
enhancer = ImageEnhance.Color(original)
colored = enhancer.enhance(1.2)
enhancer = ImageEnhance.Contrast(colored)
contrasted = enhancer.enhance(1.15)

# Ya<PERSON><PERSON> i<PERSON><PERSON> katman oluştur
txt_layer = Image.new("RGBA", contrasted.size, (255,255,255,0))
draw = ImageDraw.Draw(txt_layer)

# Fontlar (bilgisayarındaki fontları kullanabilirsin)
font_title = ImageFont.truetype("DejaVuSans-Bold.ttf", 50)
font_sub = ImageFont.truetype("DejaVuSans.ttf", 28)

# Metin içeriği
title = "254.000 KM’de Hâlâ Güçlü"
subtitle = "Fiat Stilo 1.6 16V | Bordeaux Old Rose\n2010'dan beri aynı elde"

# Konumlandırma
W, H = contrasted.size
draw.text((W/2, H*0.08), title, font=font_title, anchor="mm", fill=(255,255,255,240))
draw.text((W/2, H*0.18), subtitle, font=font_sub, anchor="mm", fill=(255,255,255,200))

# Katmanları birleştir
combined = Image.alpha_composite(contrasted, txt_layer)
final_image = combined.convert("RGB")
final_image.save("fiat_stilo_social.jpg")

# Önizleme
plt.imshow(final_image)
plt.axis("off")
plt.title("Sosyal Medya Görseli")
plt.show()
